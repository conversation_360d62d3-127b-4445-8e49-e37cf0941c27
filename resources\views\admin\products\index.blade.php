 @extends('layouts/layoutMaster')

 @section('title', __('Products'))

 <!-- Vendor Styles -->
 @section('vendor-style')

     @vite(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss', 'resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/@form-validation/form-validation.scss', 'resources/assets/vendor/libs/animate-css/animate.scss', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'])

     @vite(['resources/css/app.css'])
 @endsection

 <!-- Vendor Scripts -->
 @section('vendor-script')

     @vite(['resources/assets/vendor/libs/moment/moment.js', 'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/@form-validation/popular.js', 'resources/assets/vendor/libs/@form-validation/bootstrap5.js', 'resources/assets/vendor/libs/@form-validation/auto-focus.js', 'resources/assets/vendor/libs/cleavejs/cleave.js', 'resources/assets/vendor/libs/cleavejs/cleave-phone.js', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'])
     <script>
         const templateId = {{ $task_template->value ?? 0 }}
     </script>
 @endsection

 <!-- Page Scripts -->
 @section('page-script')
     @vite(['resources/js/admin/products/products.js'])
     @vite(['resources/js/ajax.js'])
     @vite(['resources/js/spical.js'])
 @endsection

 @section('content')

     <div class="row g-6 mb-6">
         <div class="col-sm-6 col-xl-4">
             <div class="card">
                 <div class="card-body">
                     <div class="d-flex align-items-start justify-content-between">
                         <div class="content-left">
                             <span class="text-heading">{{ __('Products') }}</span>
                             <div class="d-flex align-items-center my-1">
                                 <h4 class="mb-0 me-2" id="total"></h4>
                             </div>

                         </div>
                         <div class="avatar">
                             <span class="avatar-initial rounded bg-label-primary">
                                 <i class="ti ti-user ti-26px"></i>
                             </span>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
         <div class="col-sm-6 col-xl-4">
             <div class="card">
                 <div class="card-body">
                     <div class="d-flex align-items-start justify-content-between">
                         <div class="content-left">
                             <span class="text-heading">{{ __('Active Products') }}</span>
                             <div class="d-flex align-items-center my-1">
                                 <h4 class="mb-0 me-2" id="total-active"></h4>
                                 <p class="text-success mb-0">
                                 </p>
                             </div>

                         </div>
                         <div class="avatar">
                             <span class="avatar-initial rounded bg-label-success">
                                 <i class="ti ti-user-check ti-26px"></i>
                             </span>
                         </div>
                     </div>
                 </div>
             </div>
         </div>

         <div class="col-sm-6 col-xl-4">
             <div class="card">
                 <div class="card-body">
                     <div class="d-flex align-items-start justify-content-between">
                         <div class="content-left">
                             <span class="text-heading">{{ __('Blocked Products') }}</span>
                             <div class="d-flex align-items-center my-1">
                                 <h4 class="mb-0 me-2" id="total-blocked"></h4>
                                 <p class="text-success mb-0">
                                 </p>

                                 </p>
                             </div>

                         </div>
                         <div class="avatar">
                             <span class="avatar-initial rounded bg-label-warning">
                                 <i class="ti ti-user-search ti-26px"></i>
                             </span>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </div>
     <!-- Users List Table -->
     <div class="card">
         <div class="card-header border-bottom">
             <h5 class="card-title mb-0">
                 <i class="tf-icons ti ti-user-circle me-2 fs-3 text-white bg-primary rounded p-1"></i>
                 {{ __('Products') }}
             </h5>
             <button class="add-new btn btn-primary waves-effect waves-light mt-5 mx-4" data-bs-toggle="modal"
                 data-bs-target="#submitModal">
                 <i class="ti ti-plus me-0 me-sm-1 ti-xs"></i>
                 <span class="d-none d-sm-inline-block"> {{ __('Add New Products') }}</span>
             </button>
         </div>
         <div class="card-datatable table-responsive">
             <table class="datatables-users table">
                 <thead class="class="table-light"">
                     <tr>
                         <th></th>
                         <th>#</th>
                         <th>{{ __('image') }}</th>
                         <th>{{ __('name') }}</th>
                         <th>{{ __('code') }}</th>
                         <th>{{ __('default price') }}</th>
                         <th>{{ __('ninimum order') }}</th>
                         <th>{{ __('status') }}</th>
                         <th>{{ __('created at') }}</th>
                         <th>{{ __('actions') }}</th>
                     </tr>
                 </thead>
             </table>
         </div>

     </div>

     <div class="modal fade " id="submitModal" data-bs-backdrop="static" tabindex="-1" aria-hidden="true">
         <div class="modal-dialog modal-xl" role="document">
             <div class="modal-content">
                 <div class="modal-header">
                     <h5 class="modal-title" id="modelTitle">{{ __('Add New Products') }}</h5>
                     <button type="button" class="btn-close" data-bs-dismiss="modal"
                         aria-label="{{ __('Close') }}"></button>
                 </div>
                 <form class="add-new-user pt-0 form_submit" method="POST" action="{{ route('products.create') }}"
                     enctype="multipart/form-data">
                     <div class="modal-body">
                         <div class="col-xl-12">

                         </div>
                     </div>
                     <div class="modal-footer">
                         <button type="button" class="btn btn-label-secondary"
                             data-bs-dismiss="modal">{{ __('Close') }}</button>
                         <button type="submit" class="btn btn-primary me-3 data-submit">{{ __('Submit') }}</button>

                     </div>
                 </form>

             </div>
         </div>
     </div>



 @endsection
