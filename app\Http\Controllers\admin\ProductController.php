<?php

namespace App\Http\Controllers\admin;

use Exception;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\FunctionsController;

class ProductController extends Controller
{
    public function index()
    {
        return view('admin.products.index');
    }

    public function getData(Request $request)
    {
        $columns = [
          1 => 'id',
          2 => 'image',
          3 => 'name',
          4 => 'code',
          5 => 'price',
          6 => 'min',
          7 => 'status',
          8 => 'created_at'
        ];

        $totalData = Product::count();
        $totalFiltered = $totalData;

        $limit  = $request->input('length');
        $start  = $request->input('start');
        $order  = $columns[$request->input('order.0.column')] ?? 'id';
        $dir    = $request->input('order.0.dir') ?? 'desc';

        $search = $request->input('search');
        $statusFilter = $request->input('status');

        $query = Product::query();

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('id', 'LIKE', "%{$search}%")
                  ->orWhere('name', 'LIKE', "%{$search}%")
                  ->orWhere('code', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%");
            });
        }
        if (!empty($statusFilter)) {
            $query->where('status', $statusFilter);
        }

        $totalFiltered = $query->count();


        $products = $query
          ->offset($start)
          ->limit($limit)
          ->orderBy($order, $dir)
          ->get();

        $data = [];
        $fakeId = $start;

        foreach ($products as $product) {
            $data[] = [
              'id'         => $product->id,
              'fake_id'    => ++$fakeId,
              'name'       => $product->name,
              'code'      => $product->code,
              'image'     => $product->image ? url($product->image) : '',
              'price'      => $product->price,
              'min'      => $product->minimum_order . ' ' . $product->unit,
              'status'       => $product->status,
              'created_at' => $product->created_at->format('Y-m-d H:i'),
            ];
        }


        return response()->json([
          'draw'            => intval($request->input('draw')),
          'recordsTotal'    => $totalData,
          'recordsFiltered' => $totalFiltered,
          'code'            => 200,
          'data'            => $data,
          'summary' => [
            'total' => Product::count(),
            'total_active' => Product::where('status', 1)->count(),
            'total_blocked' => Product::where('status', 0)->count(),
          ]
        ]);
    }

    public function show($id, $name)
    {
        $data = Product::findOrFail($id);
        return view('admin.products.show', compact('data'));
    }

    public function chang_status(Request $req)
    {
        $find = Product::findOrFail($req->id);
        if (!$find) {
            return response()->json(['status' => 2, 'error' => __('Product not found')]);
        }
        $status = $find->status == 1 ? 0 : 1;
        $done = $find->update([
          'status' => $status,
        ]);
        if (!$done) {
            return response()->json(['status' => 2, 'error' => __('Error to change Product status')]);
        }
        return response()->json(['status' => 1, 'success' => $status]);
    }


    public function edit($id)
    {
        $data = Product::findOrFail($id);
        $data->img = $data->image ? url($data->image) : null;
        return response()->json($data);
    }

    public function store(Request $req)
    {
        $validator = Validator::make($req->all(), [
          'id' => 'nullable|exists:products,id',
          'name' => 'required|unique:products,name,' .  ($req->id ?? 0),
          'code' => 'required|unique:products,code,' .  ($req->id ?? 0),
          'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
          'min' => 'required|numeric|min:1',
          'price' => 'required|numeric|min:0',
          'unit' => 'required|string|in:kg,ton',
          'contact_name' => 'nullable|string|max:255',
          'contact_phone' => 'nullable|string|max:255',
          'contact_email' => 'nullable|email|max:255',
          'latitude' => 'required|numeric',
          'longitude' => 'required|numeric',
          'description' => 'nullable|string',
          'notes' => 'nullable|string',
          'address' => 'nullable|string',
        ]);
        if ($validator->fails()) {
            return response()->json(['status' => 0, 'error' => $validator->errors()->toArray()]);
        }
        DB::beginTransaction();
        try {
            $data = [
              'name' => $req->name,
              'code' => $req->code,
              'unit' => $req->unit,
              'minimum_order' => $req->min,
              'price' => $req->price,
              'contact_name' => $req->contact_name,
              'contact_phone' => $req->contact_phone,
              'contact_email' => $req->contact_email,
              'latitude' => $req->latitude,
              'longitude' => $req->longitude,
              'description' => $req->description,
              'notes' => $req->notes,
              'address' => $req->address,
            ];

            if ($req->filled('id')) {
                $find = Product::findOrFail($req->id);
                if (!$find) {
                    return response()->json(['status' => 2, 'error' => __('Can not find the selected Product')]);
                }
                $oldImage = $find->image;

                if ($req->hasFile('image')) {
                    $data['image'] = (new FunctionsController())->convert($req->image, 'products');
                }

                $done = $find->update($data);

            } else {
                if ($req->hasFile('image')) {
                    $data['image'] = (new FunctionsController())->convert($req->image, 'products');
                }

                $done = Product::create($data);

            }

            if (!$done) {
                DB::rollBack();
                return response()->json(['status' => 2, 'error' => __('Error: can not save the Product')]);
            }

            if ($oldImage && $req->hasFile('image')) {
                unlink($oldImage);
            }

            DB::commit();
            return response()->json(['status' => 1, 'success' => __('Product saved successfully')]);
        } catch (Exception $ex) {
            DB::rollBack();
            return response()->json(['status' => 2, 'error' => $ex->getMessage()]);
        }
    }



    public function destroy(Request $req)
    {
        DB::beginTransaction();
        try {
            $find = Product::with(['pricing', 'vehicles', 'sales'])->findOrFail($req->id);

            if (!$find) {
                return response()->json(['status' => 2, 'error' => __('Can not find the selected Product')]);
            }
            // التحقق من النشاطات المرتبطة
            $hasRelations =
              $find->pricing()->exists() ||
              $find->vehicles()->exists() ||
              $find->sales()->exists();

            if (!$hasRelations) {
                $find->forceDelete();
            } else {
                return response()->json(['status' => 2, 'error' => __('Can not Delete the selected Product')]);
            }

            DB::commit();
            return response()->json(['status' => 1, 'success' => __('Product deleted successfully')]);
        } catch (Exception $ex) {
            DB::rollBack();
            return response()->json(['status' => 2, 'error' => $ex->getMessage()]);
        }
    }

}
